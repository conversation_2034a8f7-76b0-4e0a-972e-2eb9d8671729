# Copyright (C) 2024 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

MANIFEST := $(LOCAL_DIR)/manifest.json

MODULE_INCLUDES := $(LOCAL_DIR)

# 只在编译 tongsuo_test TA 时包含 Tongsuo 头文件
MODULE_CFLAGS += -Iopensource_libs/Tongsuo/include
MODULE_CFLAGS += -Iopensource_libs/Tongsuo/providers/common/include
MODULE_CFLAGS += -Iopensource_libs/Tongsuo/providers/implementations/include
MODULE_CFLAGS += -DTONGSUO_SYMBOL_PREFIX

MODULE_SRCS := \
	$(LOCAL_DIR)/tongsuo_test_ta.c \
	$(LOCAL_DIR)/user_ta_header.c \

MODULE_LIBRARY_DEPS := \
	user/base/lib/libc-rctee \
	user/base/lib/libutee \
	kernel/rctee/lib/libc-ext \
	opensource_libs/Tongsuo \

include make/ta.mk
