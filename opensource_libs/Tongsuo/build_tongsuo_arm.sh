#!/bin/bash

# 铜锁ARM交叉编译脚本
# 基于官方文档配置，适用于rctee TEE系统

set -e

# 检查工具链路径
COMPILER_PATH=${COMPILER_PATH:-/home/<USER>/codebase/trusty/prebuilts}
if [ ! -d "$COMPILER_PATH" ]; then
    echo "错误: 工具链路径不存在: $COMPILER_PATH"
    echo "请设置正确的 COMPILER_PATH 环境变量"
    exit 1
fi

# 设置工具链环境变量
export CLANG_BINDIR=$COMPILER_PATH/clang/host/linux-x86/clang-r475365b/bin
export CC=$CLANG_BINDIR/clang
export CXX=$CLANG_BINDIR/clang++
export AR=$CLANG_BINDIR/llvm-ar
export RANLIB=$CLANG_BINDIR/llvm-ranlib
export STRIP=$CLANG_BINDIR/llvm-strip
export NM=$CLANG_BINDIR/llvm-nm
export OBJDUMP=$CLANG_BINDIR/llvm-objdump

# 检查工具链是否存在
if [ ! -f "$CC" ]; then
    echo "错误: 找不到clang编译器: $CC"
    exit 1
fi

echo "使用工具链: $CLANG_BINDIR"
echo "CC: $CC"
echo "CXX: $CXX"

# 设置目标架构和编译选项
TARGET_ARCH=${TARGET_ARCH:-aarch64}
INSTALL_PREFIX=${INSTALL_PREFIX:-$(pwd)/build-arm64}
SYMBOL_PREFIX=${SYMBOL_PREFIX:-"TONGSUO_"}

# ARM64交叉编译配置
if [ "$TARGET_ARCH" = "aarch64" ]; then
    CONFIGURE_TARGET="linux-aarch64"
    # 不使用交叉编译前缀，直接使用clang的target参数
    CROSS_COMPILE_PREFIX=""
    CFLAGS="-target aarch64-linux-android -march=armv8-a"
elif [ "$TARGET_ARCH" = "arm" ]; then
    CONFIGURE_TARGET="linux-armv4"
    # 不使用交叉编译前缀，直接使用clang的target参数
    CROSS_COMPILE_PREFIX=""
    CFLAGS="-target arm-linux-androideabi -march=armv7-a -mfloat-abi=softfp"
else
    echo "错误: 不支持的目标架构: $TARGET_ARCH"
    echo "支持的架构: aarch64, arm"
    exit 1
fi

# 设置编译标志
export CFLAGS="$CFLAGS -fPIC -Os -ffunction-sections -fdata-sections -Wno-array-bounds -Wno-error"
export CXXFLAGS="$CFLAGS"
export LDFLAGS="-Wl,--gc-sections"

# 清理之前的构建
echo "清理之前的构建..."
make clean 2>/dev/null || true
rm -rf $INSTALL_PREFIX

# 配置铜锁编译
echo "配置铜锁编译..."
echo "目标架构: $TARGET_ARCH"
echo "配置目标: $CONFIGURE_TARGET"
echo "安装路径: $INSTALL_PREFIX"

# 构建配置参数 - 只编译加密库，完全禁用SSL，禁用汇编优化，使用OpenSSL 1.1.1兼容API
CONFIGURE_ARGS="$CONFIGURE_TARGET \
    --prefix=$INSTALL_PREFIX \
    --openssldir=$INSTALL_PREFIX/ssl \
    --symbol-prefix=$SYMBOL_PREFIX \
    --api=1.1.1 \
    enable-sm2 \
    enable-sm3 \
    enable-sm4 \
    no-shared \
    no-dso \
    no-engine \
    no-async \
    no-sock \
    no-dgram \
    no-stdio \
    no-autoload-config \
    no-tests \
    no-threads \
    no-ui-console \
    no-ssl \
    no-tls \
    no-dtls \
    no-ssl3 \
    no-tls1 \
    no-tls1_1 \
    no-tls1_2 \
    no-tls1_3 \
    no-dtls1 \
    no-dtls1_2 \
    no-ssl-trace \
    no-asm \
    -DOPENSSL_NO_STDIO \
    -DOPENSSL_NO_SOCK \
    -DOPENSSL_NO_THREADS \
    -D__TEE__ \
    -DOPENSSL_API_COMPAT=0x10101000L"

echo "启用符号前缀: $SYMBOL_PREFIX"
echo "禁用汇编优化: 使用纯C实现"
echo "API兼容性: OpenSSL 1.1.1"

# 如果使用交叉编译前缀
if [ -n "$CROSS_COMPILE_PREFIX" ]; then
    CONFIGURE_ARGS="$CONFIGURE_ARGS --cross-compile-prefix=$CROSS_COMPILE_PREFIX-"
fi

# 根据官方文档进行配置
echo "配置参数: $CONFIGURE_ARGS"
./Configure $CONFIGURE_ARGS

if [ $? -ne 0 ]; then
    echo "错误: 配置失败"
    exit 1
fi

echo "配置完成，开始编译..."

# 编译所有库，然后只保留加密库
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "错误: 编译失败"
    exit 1
fi

echo "编译完成，开始安装..."

# 只安装加密库相关文件
mkdir -p $INSTALL_PREFIX/lib
mkdir -p $INSTALL_PREFIX/include
cp libcrypto.a $INSTALL_PREFIX/lib/
# 不复制libssl.a
cp -r include/openssl $INSTALL_PREFIX/include/

if [ $? -ne 0 ]; then
    echo "错误: 安装失败"
    exit 1
fi

echo "=========================================="
echo "铜锁ARM加密库编译完成! (带符号前缀)"
echo "=========================================="
echo "安装路径: $INSTALL_PREFIX"
echo "头文件: $INSTALL_PREFIX/include"
echo "加密库文件: $INSTALL_PREFIX/lib"
echo "符号前缀: $SYMBOL_PREFIX"

# 显示编译结果
echo ""
echo "编译结果:"
ls -la $INSTALL_PREFIX/lib/
echo ""
echo "头文件:"
ls -la $INSTALL_PREFIX/include/openssl/ | head -10

echo ""
echo "符号前缀验证:"
if [ -f "$INSTALL_PREFIX/include/openssl/symbol_prefix.h" ]; then
    echo "✅ 符号前缀头文件已生成: symbol_prefix.h"
    echo "前几个符号定义:"
    head -10 $INSTALL_PREFIX/include/openssl/symbol_prefix.h
else
    echo "⚠️  符号前缀头文件未找到"
fi

echo ""
echo "使用说明:"
echo "1. 包含头文件: -I$INSTALL_PREFIX/include"
echo "2. 链接库文件: $INSTALL_PREFIX/lib/libcrypto.a"
echo "3. 编译定义: -DOPENSSL_NO_STDIO -DOPENSSL_NO_SOCK -DOPENSSL_NO_THREADS -D__TEE__ -DOPENSSL_API_COMPAT=0x10101000L"
echo "4. 符号前缀: 所有铜锁符号都有前缀 $SYMBOL_PREFIX"
echo "5. 汇编优化: 已禁用，使用纯C实现以确保兼容性"
echo "6. API兼容性: 兼容OpenSSL 1.1.1 API"
echo ""
echo "✅ 可与其他OpenSSL版本共存!"

# 显示编译结果
echo ""
echo "编译结果:"
ls -la $INSTALL_PREFIX/lib/
echo ""
echo "头文件:"
ls -la $INSTALL_PREFIX/include/openssl/ | head -10
