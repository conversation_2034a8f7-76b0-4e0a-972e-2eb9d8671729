/*
 * WARNING: do not edit!
 * Generated configuration for Tongsu<PERSON> in Trusty TEE environment
 *
 * Copyright 2016-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file <PERSON><PERSON>EN<PERSON> in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OPENSSL_CONFIGURATION_H
# define OPENSSL_CONFIGURATION_H
# pragma once

# ifdef  __cplusplus
extern "C" {
# endif

# ifdef OPENSSL_ALGORITHM_DEFINES
#  error OPENSSL_ALGORITHM_DEFINES no longer supported
# endif

/*
 * Tongsuo was configured for Trusty TEE with the following options:
 */

# define OPENSSL_CONFIGURED_API 10101

/* Trusty-specific defines */
# ifndef OPENSSL_SYS_TRUSTY
#  define OPENSSL_SYS_TRUSTY
# endif

/* Disable features not needed in TEE environment */
# ifndef OPENSSL_NO_APPS
#  define OPENSSL_NO_APPS
# endif
# ifndef OPENSSL_NO_CAMELLIA
#  define OPENSSL_NO_CAMELLIA
# endif
# ifndef OPENSSL_NO_CAPIENG
#  define OPENSSL_NO_CAPIENG
# endif
# ifndef OPENSSL_NO_CAST
#  define OPENSSL_NO_CAST
# endif
# ifndef OPENSSL_NO_CMS
#  define OPENSSL_NO_CMS
# endif
# ifndef OPENSSL_NO_COMP
#  define OPENSSL_NO_COMP
# endif
# ifndef OPENSSL_NO_CONF
#  define OPENSSL_NO_CONF
# endif
# ifndef OPENSSL_NO_DES
#  define OPENSSL_NO_DES
# endif
# ifndef OPENSSL_NO_DTLS
#  define OPENSSL_NO_DTLS
# endif
# ifndef OPENSSL_NO_DTLS1
#  define OPENSSL_NO_DTLS1
# endif
# ifndef OPENSSL_NO_ERR
#  define OPENSSL_NO_ERR
# endif
# ifndef OPENSSL_NO_GOST
#  define OPENSSL_NO_GOST
# endif
# ifndef OPENSSL_NO_GMP
#  define OPENSSL_NO_GMP
# endif
# ifndef OPENSSL_NO_HEARTBEATS
#  define OPENSSL_NO_HEARTBEATS
# endif
# ifndef OPENSSL_NO_IDEA
#  define OPENSSL_NO_IDEA
# endif
# ifndef OPENSSL_NO_JPAKE
#  define OPENSSL_NO_JPAKE
# endif
# ifndef OPENSSL_NO_LOCKING
#  define OPENSSL_NO_LOCKING
# endif
# ifndef OPENSSL_NO_MD2
#  define OPENSSL_NO_MD2
# endif
# ifndef OPENSSL_NO_MD4
#  define OPENSSL_NO_MD4
# endif
# ifndef OPENSSL_NO_MDC2
#  define OPENSSL_NO_MDC2
# endif
# ifndef OPENSSL_NO_OCSP
#  define OPENSSL_NO_OCSP
# endif
# ifndef OPENSSL_NO_PEM
#  define OPENSSL_NO_PEM
# endif
# ifndef OPENSSL_NO_PKCS12
#  define OPENSSL_NO_PKCS12
# endif
# ifndef OPENSSL_NO_PQUEUE
#  define OPENSSL_NO_PQUEUE
# endif
# ifndef OPENSSL_NO_RC2
#  define OPENSSL_NO_RC2
# endif
# ifndef OPENSSL_NO_RC5
#  define OPENSSL_NO_RC5
# endif
# ifndef OPENSSL_NO_RDRAND
#  define OPENSSL_NO_RDRAND
# endif
# ifndef OPENSSL_NO_RFC3779
#  define OPENSSL_NO_RFC3779
# endif
# ifndef OPENSSL_NO_RIPEMD
#  define OPENSSL_NO_RIPEMD
# endif
# ifndef OPENSSL_NO_RSAX
#  define OPENSSL_NO_RSAX
# endif
# ifndef OPENSSL_NO_SCTP
#  define OPENSSL_NO_SCTP
# endif
# ifndef OPENSSL_NO_SEED
#  define OPENSSL_NO_SEED
# endif
# ifndef OPENSSL_NO_SHA0
#  define OPENSSL_NO_SHA0
# endif
# ifndef OPENSSL_NO_SRP
#  define OPENSSL_NO_SRP
# endif
# ifndef OPENSSL_NO_SSL
#  define OPENSSL_NO_SSL
# endif
# ifndef OPENSSL_NO_TLS
#  define OPENSSL_NO_TLS
# endif
# ifndef OPENSSL_NO_STATIC_ENGINE
#  define OPENSSL_NO_STATIC_ENGINE
# endif
# ifndef OPENSSL_NO_STORE
#  define OPENSSL_NO_STORE
# endif
# ifndef OPENSSL_NO_TS
#  define OPENSSL_NO_TS
# endif
# ifndef OPENSSL_NO_TXT_DB
#  define OPENSSL_NO_TXT_DB
# endif
# ifndef OPENSSL_NO_UI
#  define OPENSSL_NO_UI
# endif
# ifndef OPENSSL_NO_UNIT_TEST
#  define OPENSSL_NO_UNIT_TEST
# endif
# ifndef OPENSSL_NO_WHIRLPOOL
#  define OPENSSL_NO_WHIRLPOOL
# endif
# ifndef OPENSSL_NO_DEPRECATED
#  define OPENSSL_NO_DEPRECATED
# endif
# ifndef OPENSSL_NO_DEPRECATED_3_0
#  define OPENSSL_NO_DEPRECATED_3_0
# endif

/* Enable Chinese national cryptography algorithms */
# ifndef OPENSSL_ENABLE_SM2
#  define OPENSSL_ENABLE_SM2
# endif
# ifndef OPENSSL_ENABLE_SM3
#  define OPENSSL_ENABLE_SM3
# endif
# ifndef OPENSSL_ENABLE_SM4
#  define OPENSSL_ENABLE_SM4
# endif

/* Trusty environment specific settings */
# ifndef OPENSSL_NO_FILESYSTEM
#  define OPENSSL_NO_FILESYSTEM
# endif
# ifndef OPENSSL_NO_POSIX_IO
#  define OPENSSL_NO_POSIX_IO
# endif
# ifndef OPENSSL_NO_SOCK
#  define OPENSSL_NO_SOCK
# endif
# ifndef OPENSSL_NO_THREADS
#  define OPENSSL_NO_THREADS
# endif

/* Generate 80386 code? */
# undef I386_ONLY

/*
 * The following are cipher-specific, but are part of the public API.
 */
# if !defined(OPENSSL_SYS_UEFI)
#  define BN_LLONG
/* Only one for the following should be defined */
#  undef SIXTY_FOUR_BIT_LONG
#  define SIXTY_FOUR_BIT
#  undef THIRTY_TWO_BIT
# endif

# define RC4_INT unsigned int

# ifdef  __cplusplus
}
# endif

#endif                          /* OPENSSL_CONFIGURATION_H */
