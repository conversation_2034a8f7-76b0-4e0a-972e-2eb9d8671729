#!/usr/bin/env python3
"""
Script to merge complete source file list from sources.mk.in into Crypto-config-trusty.mk
while preserving other configurations (cflags, includes, etc.)
"""

import re
import sys

def extract_source_files(sources_mk_in_path):
    """Extract source files from sources.mk.in"""
    with open(sources_mk_in_path, 'r') as f:
        content = f.read()

    # Define modules to exclude for Trusty TEE environment
    excluded_modules = [
        'crypto/cms/',      # OPENSSL_NO_CMS
        'crypto/comp/',     # OPENSSL_NO_COMP
        'crypto/conf/',     # OPENSSL_NO_CONF
        'crypto/des/',      # OPENSSL_NO_DES
        'crypto/ocsp/',     # OPENSSL_NO_OCSP
        'crypto/pem/',      # OPENSSL_NO_PEM
        'crypto/pkcs12/',   # OPENSSL_NO_PKCS12
        'crypto/ts/',       # OPENSSL_NO_TS
        'crypto/ui/',       # OPENSSL_NO_UI
        'crypto/store/',    # OPENSSL_NO_STORE
        'crypto/srp/',      # OPENSSL_NO_SRP
    ]

    # Find the source files section
    lines = content.split('\n')
    source_files = []
    in_source_section = False

    for line in lines:
        if 'LOCAL_SRC_FILES_aarch64 :=' in line:
            in_source_section = True
            continue
        elif in_source_section:
            if line.strip() == '' or 'crypto_headers :=' in line:
                break
            # Remove leading spaces and trailing backslash
            clean_line = line.strip().rstrip('\\')
            if clean_line and clean_line.endswith('.c'):
                # Check if this file should be excluded
                should_exclude = any(excluded_module in clean_line for excluded_module in excluded_modules)
                if not should_exclude:
                    source_files.append('  ' + clean_line + ' \\')

    # Remove trailing backslash from last file
    if source_files:
        source_files[-1] = source_files[-1].rstrip(' \\') + ' \\'

    return source_files

def update_crypto_config(crypto_config_path, source_files):
    """Update Crypto-config-trusty.mk with new source files"""
    with open(crypto_config_path, 'r') as f:
        lines = f.readlines()
    
    # Find the common_src_files section
    start_idx = None
    end_idx = None
    
    for i, line in enumerate(lines):
        if 'common_src_files :=' in line:
            start_idx = i + 1
        elif start_idx is not None and ('common_c_includes :=' in line or line.strip().startswith('common_') or line.strip().startswith('arm_')):
            end_idx = i
            break
    
    if start_idx is None:
        print("Error: Could not find common_src_files section")
        return False
    
    if end_idx is None:
        end_idx = len(lines)
    
    # Replace the source files section
    new_lines = lines[:start_idx] + [line + '\n' for line in source_files] + ['\n'] + lines[end_idx:]
    
    with open(crypto_config_path, 'w') as f:
        f.writelines(new_lines)
    
    return True

def main():
    sources_mk_in = 'sources.mk.in'
    crypto_config = 'Crypto-config-trusty.mk'
    
    print("Extracting source files from sources.mk.in...")
    source_files = extract_source_files(sources_mk_in)
    print(f"Found {len(source_files)} source files")
    
    print("Updating Crypto-config-trusty.mk...")
    if update_crypto_config(crypto_config, source_files):
        print("Successfully updated Crypto-config-trusty.mk")
        print(f"Added {len(source_files)} source files to common_src_files")
    else:
        print("Failed to update Crypto-config-trusty.mk")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
